# Koyeb-Based Preview Implementation (Docker + HTTP Code Injection)

## Overview

This document outlines the migration from Docker-based preview builds to Koyeb-based ephemeral dev server previews using pre-built Docker images with HTTP-based code injection. The new system will provide sub-5-second preview functionality with 2-minute lifespans.

## Architecture Flow

```
Frontend (/upload) → Backend → Koyeb App + Web Service → HTTP Code Injection → Dev Server Ready
     ↓                ↓              ↓                        ↓                    ↓
  File Tree      Convert to      Deploy Container        POST /inject-code    App URL Preview
   (JSON)        File Map         (~2-3 seconds)         (~1 second)        (Sub-5 seconds)
```

## Current System Analysis

### Current Architecture

- **Upload Endpoint**: `POST /upload` in `apps/worker/src/server.ts`
- **Build Process**: Docker containers with esbuild/SWC compilation
- **Storage**: File-based output storage with static builds
- **Preview**: Static HTML files served from build output
- **Cleanup**: Manual container management

### Current Flow

1. Frontend sends `updatedFileTree` to `/upload`
2. Worker creates Docker container with pre-installed dependencies
3. Files are written to container filesystem
4. Build process runs (esbuild/SWC compilation)
5. Static files generated and stored
6. Preview URL returned pointing to static files

## Core Concept

Instead of uploading directories, we use:

1. **Pre-built Docker image** with Next.js + all dependencies pre-installed
2. **HTTP API endpoint** in container for receiving AI-generated code
3. **Instant deployment** since no build/upload time needed
4. **Code injection** after container is running
5. **Sub-5-second total time** from request to preview

## Koyeb Architecture Understanding

**Hierarchy:**

- **App** (top-level container with domain)
  - **Web Service** (runs the Docker container)
    - **Deployments** (specific instances)

**Key Points:**

- We create an **App** first, then a **Web Service** inside it
- The **App URL** is used for preview (not service URL)
- Deleting the **App** automatically deletes all its services
- Each preview gets a fresh App + Web Service for isolation

## Key Components

### 1. Pre-built Docker Base Image

```dockerfile
# apps/worker/docker/nextjs-base/Dockerfile
FROM node:18-alpine

WORKDIR /app

# Install dependencies globally for faster access
RUN npm install -g pnpm@8

# Create base Next.js project with all common dependencies
COPY package.json pnpm-lock.yaml ./
RUN pnpm install --frozen-lockfile

# Copy base template files
COPY . .

# Install code injection server dependencies
RUN pnpm add express cors fs-extra

# Expose ports
EXPOSE 3000 8080

# Copy startup script
COPY startup.js ./
RUN chmod +x startup.js

# Start with code injection server
CMD ["node", "startup.js"]
```

### 2. Code Injection Server

```javascript
// apps/worker/docker/nextjs-base/startup.js
const express = require('express');
const fs = require('fs-extra');
const path = require('path');
const { spawn } = require('child_process');
const cors = require('cors');

const app = express();
app.use(cors());
app.use(express.json({ limit: '50mb' }));

let devServerProcess = null;

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    devServer: !!devServerProcess,
    timestamp: new Date().toISOString(),
  });
});

// Code injection endpoint
app.post('/inject-code', async (req, res) => {
  try {
    const { files } = req.body;

    if (!files || typeof files !== 'object') {
      return res.status(400).json({ error: 'Files object required' });
    }

    console.log(`Injecting ${Object.keys(files).length} files...`);

    // Kill existing dev server
    if (devServerProcess) {
      devServerProcess.kill('SIGTERM');
      devServerProcess = null;
    }

    // Write all files
    for (const [filePath, content] of Object.entries(files)) {
      const fullPath = path.join('/app', filePath);
      await fs.ensureDir(path.dirname(fullPath));
      await fs.writeFile(fullPath, content || '');
    }

    console.log('Files written successfully');

    // Start dev server
    devServerProcess = spawn('pnpm', ['dev'], {
      cwd: '/app',
      stdio: 'pipe',
      env: { ...process.env, PORT: '3000' },
    });

    devServerProcess.stdout.on('data', (data) => {
      console.log(`Dev server: ${data}`);
    });

    devServerProcess.stderr.on('data', (data) => {
      console.error(`Dev server error: ${data}`);
    });

    devServerProcess.on('close', (code) => {
      console.log(`Dev server exited with code ${code}`);
      devServerProcess = null;
    });

    // Wait a moment for server to start
    setTimeout(() => {
      res.json({
        success: true,
        message: 'Code injected and dev server started',
        filesCount: Object.keys(files).length,
      });
    }, 2000);
  } catch (error) {
    console.error('Code injection failed:', error);
    res.status(500).json({
      error: 'Code injection failed',
      details: error.message,
    });
  }
});

// Start code injection server
app.listen(8080, () => {
  console.log('Code injection API listening on port 8080');
  console.log('Waiting for code injection...');
});
```

### 3. Base Package.json with All Dependencies

```json
{
  "name": "koyeb-nextjs-preview",
  "version": "1.0.0",
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start"
  },
  "dependencies": {
    "next": "^14.0.0",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "@types/node": "^20.0.0",
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "typescript": "^5.0.0",
    "tailwindcss": "^3.3.0",
    "autoprefixer": "^10.4.0",
    "postcss": "^8.4.0",
    "lucide-react": "^0.263.0",
    "clsx": "^2.0.0",
    "class-variance-authority": "^0.7.0",
    "@tailwindcss/forms": "^0.5.0",
    "@tailwindcss/typography": "^0.5.0",
    "framer-motion": "^10.0.0",
    "date-fns": "^2.30.0"
  },
  "devDependencies": {
    "eslint": "^8.0.0",
    "eslint-config-next": "^14.0.0"
  }
}
```

### 4. Koyeb Service Manager

```typescript
// apps/worker/src/koyeb/koyeb-service.ts

interface KoyebApp {
  id: string;
  name: string;
  created_at: string;
  updated_at: string;
}

interface KoyebService {
  id: string;
  name: string;
  app_id: string;
  status: string;
  latest_deployment_id: string;
  created_at: string;
  updated_at: string;
}

interface KoyebDeployment {
  id: string;
  service_id: string;
  status: string;
  messages: string[];
  definition: {
    name: string;
    type: string;
    regions: string[];
    instance_types: {
      type: string;
    };
    docker: {
      image: string;
    };
    ports: Array<{
      port: number;
      protocol: string;
    }>;
    env: Array<{
      key: string;
      value: string;
    }>;
    health_checks: Array<{
      http: {
        port: number;
        path: string;
      };
    }>;
    scaling: {
      min: number;
      max: number;
    };
  };
}

export class KoyebPreviewManager {
  private apiKey: string;
  private baseUrl = 'https://app.koyeb.com/v1';
  private dockerImage: string;

  constructor(apiKey: string, dockerImage: string) {
    this.apiKey = apiKey;
    this.dockerImage = dockerImage;
  }

  private async makeRequest<T>(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    body?: any,
  ): Promise<T> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method,
      headers: {
        Authorization: `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: body ? JSON.stringify(body) : undefined,
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Koyeb API error: ${response.status} - ${error}`);
    }

    return response.json();
  }

  async createApp(appName: string): Promise<KoyebApp> {
    return this.makeRequest<{ app: KoyebApp }>('/apps', 'POST', {
      name: appName,
    }).then((response) => response.app);
  }

  async createPreviewApp(
    projectData: any,
  ): Promise<{ serviceId: string; appId: string; appUrl: string }> {
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substr(2, 9);
    const appName = `preview-app-${timestamp}-${randomId}`;
    const serviceName = `preview-web-service-${timestamp}-${randomId}`;

    // Step 1: Create App (top-level container)
    const app = await this.createApp(appName);

    // Step 2: Create Web Service inside the App
    const servicePayload = {
      app_id: app.id,
      definition: {
        name: serviceName,
        type: 'WEB', // This creates a web service inside the app
        regions: ['fra'],
        instance_types: {
          type: 'nano',
        },
        docker: {
          image: this.dockerImage,
        },
        ports: [
          {
            port: 3000,
            protocol: 'http',
          },
          {
            port: 8080,
            protocol: 'http',
          },
        ],
        env: [
          {
            key: 'NODE_ENV',
            value: 'development',
          },
        ],
        health_checks: [
          {
            http: {
              port: 8080,
              path: '/health',
            },
          },
        ],
        scaling: {
          min: 1,
          max: 1,
        },
      },
    };

    const service = await this.makeRequest<{ service: KoyebService }>(
      '/services',
      'POST',
      servicePayload,
    ).then((response) => response.service);

    // Step 3: Wait for web service to be ready
    await this.waitForServiceReady(service.id);

    // Step 4: Get App URL (this is what we use for preview)
    const appUrl = `https://${app?.domains?.[0]?.name ?? ''}`;

    // Schedule auto-destruction after 2 minutes (delete entire app)
    setTimeout(
      () => {
        this.destroyApp(app.id);
      },
      parseInt(process.env.PREVIEW_TIMEOUT || '120000'),
    );

    return {
      serviceId: service.id,
      appId: app.id,
      appUrl, // App URL, not service URL
    };
  }

  async getService(serviceId: string): Promise<KoyebService> {
    return this.makeRequest<{ service: KoyebService }>(
      `/services/${serviceId}`,
    ).then((response) => response.service);
  }

  async getDeployment(deploymentId: string): Promise<KoyebDeployment> {
    return this.makeRequest<{ deployment: KoyebDeployment }>(
      `/deployments/${deploymentId}`,
    ).then((response) => response.deployment);
  }

  async waitForServiceReady(serviceId: string): Promise<void> {
    let attempts = 0;
    const maxAttempts = 24; // 4 minutes max wait (24 * 10s)

    while (attempts < maxAttempts) {
      try {
        const service = await this.getService(serviceId);

        if (service.latest_deployment_id) {
          const deployment = await this.getDeployment(
            service.latest_deployment_id,
          );

          if (deployment.status === 'HEALTHY') {
            console.log(
              `Web service is ready! Service ${serviceId} is healthy`,
            );
            return;
          }

          if (
            deployment.status === 'ERROR' ||
            deployment.status === 'STOPPED'
          ) {
            throw new Error(
              `Web service deployment failed: ${deployment.messages?.join(', ')}`,
            );
          }

          console.log(`Web service ${serviceId} status: ${deployment.status}`);
        }

        await new Promise((resolve) => setTimeout(resolve, 10000)); // Wait 10s
        attempts++;
      } catch (error) {
        console.error(`Attempt ${attempts + 1} failed:`, error);
        attempts++;
        await new Promise((resolve) => setTimeout(resolve, 10000));
      }
    }

    throw new Error('Web service failed to become ready within timeout');
  }

  async injectCode(
    appUrl: string,
    files: Record<string, string>,
  ): Promise<void> {
    const injectionUrl = `${appUrl}/inject-code`;

    const response = await fetch(injectionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Koyeb-Preview-System/1.0',
      },
      body: JSON.stringify({ files }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `Code injection failed: ${response.status} - ${errorText}`,
      );
    }

    const result = await response.json();
    console.log('Code injection successful:', result.message);
  }

  async destroyApp(appId: string): Promise<void> {
    try {
      // Delete the app - this automatically deletes all associated services
      await this.makeRequest(`/apps/${appId}`, 'DELETE');
      console.log(`App ${appId} deleted successfully (services auto-deleted)`);
    } catch (error) {
      console.error(`Failed to destroy app ${appId}:`, error);
    }
  }
}
```

### 5. File Tree to Files Converter

```typescript
// apps/worker/src/utils/file-converter.ts
export function convertFileTreeToFiles(fileTree: any): Record<string, string> {
  const files: Record<string, string> = {};

  function processNode(node: any, currentPath: string = '') {
    if (node.type === 'file') {
      files[currentPath] = node.content || '';
    } else if (node.type === 'directory' && node.children) {
      Object.entries(node.children).forEach(([name, child]) => {
        const newPath = currentPath ? `${currentPath}/${name}` : name;
        processNode(child, newPath);
      });
    }
  }

  processNode(fileTree);
  return files;
}
```

## Implementation Steps

### Phase 1: Docker Image Setup

1. **Build Base Docker Image from External Repository**

```bash
# Clone the base template repository
git clone https://github.com/your-org/nextjs-base-template.git
cd nextjs-base-template

# Create the code injection server
# Create startup.js file as shown in the "Code Injection Server" section above

# Update Dockerfile to include code injection capabilities
# Use the Dockerfile example from the "Pre-built Docker Base Image" section above

# Build and push to registry
docker build -t your-registry/nextjs-preview-base:latest .
docker push your-registry/nextjs-preview-base:latest
```

> **Note**: The base template is maintained in a separate repository, not within the core app. This allows for independent versioning and updates to the template without modifying the main application.

2. **Environment Configuration**

```bash
# Add to .env
KOYEB_API_KEY=your_koyeb_api_key
PREVIEW_TIMEOUT=120000  # 2 minutes
DOCKER_IMAGE=your-registry/nextjs-preview-base:latest
```

### Phase 2: Core App Implementation

1. **Create Koyeb Preview Module**

```bash
# Create directory structure
mkdir -p apps/core/src/preview
mkdir -p apps/core/src/preview/utils
```

2. **Implement Koyeb Service Manager**

```typescript
// apps/core/src/preview/koyeb-preview-manager.ts
// Copy the KoyebPreviewManager class from the documentation above
```

3. **Create File Converter Utility**

```typescript
// apps/core/src/preview/utils/file-converter.ts
export function convertFileTreeToFiles(fileTree: any): Record<string, string> {
  const files: Record<string, string> = {};

  function processNode(node: any, currentPath: string = '') {
    if (node.type === 'file') {
      files[currentPath] = node.content || '';
    } else if (node.type === 'directory' && node.children) {
      Object.entries(node.children).forEach(([name, child]) => {
        const newPath = currentPath ? `${currentPath}/${name}` : name;
        processNode(child, newPath);
      });
    }
  }

  processNode(fileTree);
  return files;
}
```

4. **Create Preview Service**

```typescript
// apps/core/src/preview/preview.service.ts
import { Injectable } from '@nestjs/common';
import { KoyebPreviewManager } from './koyeb-preview-manager';
import { convertFileTreeToFiles } from './utils/file-converter';

@Injectable()
export class PreviewService {
  private koyebManager: KoyebPreviewManager;

  constructor() {
    this.koyebManager = new KoyebPreviewManager(
      process.env.KOYEB_API_KEY!,
      process.env.DOCKER_IMAGE!,
    );
  }

  async createPreview(fileTree: any) {
    // Step 1: Create Koyeb app with web service (2-3 seconds)
    const { serviceId, appId, appUrl } =
      await this.koyebManager.createPreviewApp(fileTree);

    // Step 2: Convert file tree to files map
    const files = convertFileTreeToFiles(fileTree);

    // Step 3: Inject code into running container (1 second)
    await this.koyebManager.injectCode(appUrl, files);

    return {
      serviceId,
      appId,
      previewUrl: appUrl, // Using App URL
      wsUrl: `${appUrl.replace('http', 'ws')}/ws`,
    };
  }
}
```

5. **Create Preview Controller**

```typescript
// apps/core/src/preview/preview.controller.ts
import { Controller, Post, Body } from '@nestjs/common';
import { PreviewService } from './preview.service';

@Controller('preview')
export class PreviewController {
  constructor(private readonly previewService: PreviewService) {}

  @Post('upload')
  async createPreview(@Body() fileTree: any) {
    return this.previewService.createPreview(fileTree);
  }
}
```

6. **Create Preview Module**

```typescript
// apps/core/src/preview/preview.module.ts
import { Module } from '@nestjs/common';
import { PreviewController } from './preview.controller';
import { PreviewService } from './preview.service';

@Module({
  controllers: [PreviewController],
  providers: [PreviewService],
  exports: [PreviewService],
})
export class PreviewModule {}
```

7. **Import Preview Module in App Module**

```typescript
// apps/core/src/app.module.ts
import { Module } from '@nestjs/common';
import { PreviewModule } from './preview/preview.module';
// ... other imports

@Module({
  imports: [
    // ... other modules
    PreviewModule,
  ],
  // ... rest of module config
})
export class AppModule {}
```

### Phase 3: Frontend Updates

1. **Update API Endpoint in Frontend**

```typescript
// apps/admin/src/features/chat/main.tsx
// Update the API endpoint to point to the core app's preview endpoint

const response = (await api.post('/preview/upload', updatedFileTree, {
  baseURL: `http://localhost:${env.SERVER_PORT}`,
})) as UploadResponse;

const previewUrl = response.previewUrl; // This is the App URL
setPreviewUrl(previewUrl);
```

2. **Keep Full Server Capabilities**

```typescript
// apps/admin/src/features/chat/utils/convert.ts
// NO CHANGES NEEDED - keep all files including:
// ✅ API routes (route.ts files)
// ✅ Middleware files
// ✅ Original package.json scripts
// ✅ All server-side functionality

// The modifyFilesForPreview function can remain as-is or be simplified
// since we now have a full dev server that supports everything
```

## Base Template Repository Maintenance

The base template repository should be maintained separately with the following components:

1. **Next.js Base Project**: A standard Next.js project with common dependencies
2. **Code Injection Server**: The Express server for receiving and injecting code
3. **Dockerfile**: Configuration for building the container image
4. **CI/CD Pipeline**: For automatically building and publishing new versions

When updates are needed to the base template:

1. Make changes to the base template repository
2. Build and publish a new Docker image
3. Update the `DOCKER_IMAGE` environment variable in the core app

This separation allows for:

- Independent versioning of the template
- Easier testing of template changes
- Ability to maintain multiple template versions
- Cleaner separation of concerns

## Performance & Benefits

### Speed Comparison

| Step                 | Current Docker    | New Koyeb + Code Injection |
| -------------------- | ----------------- | -------------------------- |
| **Container Start**  | ~10-15 seconds    | ~2-3 seconds               |
| **Build Process**    | ~30-60 seconds    | ❌ No build needed         |
| **Code Injection**   | ❌ Not applicable | ~1 second                  |
| **Dev Server Start** | ~5-10 seconds     | ~1 second                  |
| **Total Time**       | ~45-85 seconds    | **~4-5 seconds**           |

### Key Benefits

1. **Sub-5-Second Previews**

   - Pre-built Docker image with all dependencies
   - No build/compile time needed
   - Instant code injection via HTTP
   - Fast container startup on Koyeb

2. **Full Server Capabilities**

   - API routes work perfectly
   - Middleware supported
   - SSR/SSG capabilities
   - Database connections
   - File uploads
   - Authentication flows

3. **Scalability**

   - No local Docker management
   - Koyeb handles infrastructure
   - Multiple concurrent previews
   - Automatic cleanup after 2 minutes

4. **Cost Efficiency**

   - Pay only for 2-minute usage
   - Nano instances (~$0.000046 per preview)
   - No persistent infrastructure costs

5. **Reliability**
   - Distributed Koyeb infrastructure
   - Health checks and auto-restart
   - Automatic service cleanup
   - Error handling and fallbacks

### Additional Benefits of Core App Integration

1. **Simplified Architecture**

   - Single service architecture
   - No inter-service communication needed
   - Reduced operational complexity

2. **Unified Deployment**

   - Deploy as part of the main application
   - Shared infrastructure and scaling
   - Centralized logging and monitoring

3. **Easier Configuration**
   - Single set of environment variables
   - Consistent configuration management
   - Shared dependencies

## Required Environment Variables

```bash
# Add to apps/core/.env
KOYEB_API_KEY=your_koyeb_api_key
PREVIEW_TIMEOUT=120000  # 2 minutes
DOCKER_IMAGE=your-registry/nextjs-preview-base:latest
```

## Migration Timeline

1. **Week 1**: Build and test Docker base image from external repository
2. **Week 2**: Create preview module in core app
3. **Week 3**: Update frontend to use new endpoint, test integration
4. **Week 4**: Production deployment and monitoring
5. **Week 5**: Performance optimization and cleanup automation

This integrated approach simplifies the architecture while maintaining all the benefits of the Koyeb-based preview system, resulting in sub-5-second preview generation without the complexity of a separate worker service!
