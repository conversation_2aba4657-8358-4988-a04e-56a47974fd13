import { KoyebCreateAppResponse } from './types';

interface KoyebApp {
  id: string;
  name: string;
  created_at: string;
  updated_at: string;
}

interface KoyebService {
  id: string;
  name: string;
  app_id: string;
  status: string;
  latest_deployment_id: string;
  created_at: string;
  updated_at: string;
}

interface KoyebDeployment {
  id: string;
  service_id: string;
  status: string;
  messages: string[];
  definition: {
    name: string;
    type: string;
    regions: string[];
    instance_types: Array<{
      type: string;
    }>;
    docker: {
      image: string;
    };
    ports: Array<{
      port: number;
      protocol: string;
    }>;
    env: Array<{
      key: string;
      value: string;
    }>;
    health_checks: Array<{
      http: {
        port: number;
        path: string;
      };
    }>;
  };
}

export class KoyebPreviewManager {
  private apiKey: string;
  private baseUrl = 'https://app.koyeb.com/v1';
  private dockerImage: string;

  constructor(apiKey: string, dockerImage: string) {
    this.apiKey = apiKey;
    this.dockerImage = dockerImage;
  }

  private async makeRequest<T>(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    body?: any,
  ): Promise<T> {
    console.log(
      `Making ${method} request to ${endpoint}`,
      body ? JSON.stringify(body, null, 2) : 'No body',
    );

    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method,
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: body ? JSON.stringify(body) : undefined,
      });

      const responseText = await response.text();

      if (!response.ok) {
        console.error(`Koyeb API error: ${response.status} - ${responseText}`);
        throw new Error(
          `Koyeb API error: ${response.status} - ${responseText}`,
        );
      }

      try {
        return JSON.parse(responseText);
      } catch (error) {
        console.error('Failed to parse response as JSON:', responseText);
        throw new Error(`Failed to parse Koyeb API response: ${error.message}`);
      }
    } catch (error) {
      console.error(`Request to ${endpoint} failed:`, error);
      throw error;
    }
  }

  async createApp(appName: string): Promise<KoyebApp> {
    return this.makeRequest<{ app: KoyebApp }>('/apps', 'POST', {
      name: appName,
    }).then((response) => response.app);
  }

  async createPreviewApp(
    projectData: any,
  ): Promise<{ serviceId: string; appId: string; appUrl: string }> {
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substr(2, 9);
    const appName = `preview-app-${timestamp}-${randomId}`;
    const serviceName = `preview-web-service-${timestamp}-${randomId}`;

    // Step 1: Create App (top-level container)
    const app = (await this.createApp(appName)) as KoyebCreateAppResponse;

    // Step 2: Create Web Service inside the App
    // This is what actually runs our Docker container
    const servicePayload = {
      app_id: app.id,
      definition: {
        name: serviceName,
        type: 'WEB', // This creates a web service inside the app
        routes: [
          {
            path: '/',
            port: 3000,
          },
          {
            path: '/inject-code',
            port: 8080,
          },
          {
            path: '/health',
            port: 8080,
          },
        ],
        ports: [
          {
            port: 3000,
            protocol: 'http',
          },
          {
            port: 8080,
            protocol: 'http',
          },
        ],
        env: [
          {
            key: 'NODE_ENV',
            value: 'development',
          },
        ],
        regions: ['sin'],
        instance_types: [
          {
            type: 'small',
          },
        ],
        scalings: [
          {
            min: 1,
            max: 1,
          },
        ],
        health_checks: [
          {
            http: {
              port: 8080,
              path: '/health',
            },
          },
        ],
        docker: {
          image: this.dockerImage,
        },
      },
    };

    try {
      const service = await this.makeRequest<{ service: KoyebService }>(
        '/services',
        'POST',
        servicePayload,
      ).then((response) => response.service);

      // Step 3: Wait for the web service to be ready
      await this.waitForServiceReady(service.id);

      // Step 4: Get the App URL (this is what we use for preview, not service URL)
      const appUrl = `https://${app?.domains?.[0]?.name ?? ''}`;

      // Schedule auto-destruction after 2 minutes (delete entire app)
      const TWO_MINUTES = '120000';
      setTimeout(
        () => {
          this.destroyApp(app.id);
        },
        parseInt(process.env.PREVIEW_TIMEOUT || TWO_MINUTES),
      );

      return {
        serviceId: service.id,
        appId: app.id,
        appUrl, // This is the App URL, not service URL
      };
    } catch (error) {
      console.error('Web service creation failed:', error);
      console.error(
        'Service payload:',
        JSON.stringify(servicePayload, null, 2),
      );

      // Clean up the app if service creation fails
      try {
        await this.makeRequest(`/apps/${app.id}`, 'DELETE');
        console.log(`App ${app.id} deleted after service creation failure`);
      } catch (cleanupError) {
        console.error(`Failed to clean up app ${app.id}:`, cleanupError);
      }

      throw error;
    }
  }

  async getService(serviceId: string): Promise<KoyebService> {
    return this.makeRequest<{ service: KoyebService }>(
      `/services/${serviceId}`,
    ).then((response) => response.service);
  }

  async getDeployment(deploymentId: string): Promise<KoyebDeployment> {
    return this.makeRequest<{ deployment: KoyebDeployment }>(
      `/deployments/${deploymentId}`,
    ).then((response) => response.deployment);
  }

  async waitForServiceReady(serviceId: string): Promise<void> {
    let attempts = 0;
    const maxAttempts = 24; // 4 minutes max wait (24 * 10s)

    while (attempts < maxAttempts) {
      try {
        const service = await this.getService(serviceId);

        if (service.latest_deployment_id) {
          const deployment = await this.getDeployment(
            service.latest_deployment_id,
          );

          if (deployment.status === 'HEALTHY') {
            console.log(
              `Web service is ready! Service ${serviceId} is healthy`,
            );
            return;
          }

          if (
            deployment.status === 'ERROR' ||
            deployment.status === 'STOPPED'
          ) {
            throw new Error(
              `Web service deployment failed: ${deployment.messages?.join(', ')}`,
            );
          }

          console.log(`Web service ${serviceId} status: ${deployment.status}`);
        }

        await new Promise((resolve) => setTimeout(resolve, 10000)); // Wait 10s
        attempts++;
      } catch (error) {
        console.error(`Attempt ${attempts + 1} failed:`, error);
        attempts++;
        await new Promise((resolve) => setTimeout(resolve, 10000));
      }
    }

    throw new Error('Web service failed to become ready within timeout');
  }

  async injectCode(
    appUrl: string,
    files: Record<string, string>,
  ): Promise<void> {
    // The startup.js script runs on port 8080 and handles both code injection and Next.js proxy
    const injectionUrl = `${appUrl}/inject-code`;

    const response = await fetch(injectionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Koyeb-Preview-System/1.0',
      },
      body: JSON.stringify({ files }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `Code injection failed: ${response.status} - ${errorText}`,
      );
    }

    const result = await response.json();
    console.log('Code injection successful:', result.message);
  }

  async destroyApp(appId: string): Promise<void> {
    try {
      // Delete the app - this will automatically delete all associated services
      await this.makeRequest(`/apps/${appId}`, 'DELETE');
      console.log(`App ${appId} deleted successfully (services auto-deleted)`);
    } catch (error) {
      console.error(`Failed to destroy app ${appId}:`, error);
    }
  }
}
