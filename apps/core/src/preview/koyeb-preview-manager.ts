import { KoyebCreateAppResponse } from './types';

interface KoyebApp {
  id: string;
  name: string;
  created_at: string;
  updated_at: string;
}

interface KoyebService {
  id: string;
  name: string;
  app_id: string;
  status: string;
  latest_deployment_id: string;
  created_at: string;
  updated_at: string;
}

interface KoyebDeployment {
  id: string;
  service_id: string;
  status: string;
  messages: string[];
  definition: {
    name: string;
    type: string;
    regions: string[];
    instance_types: Array<{
      type: string;
    }>;
    docker: {
      image: string;
    };
    ports: Array<{
      port: number;
      protocol: string;
    }>;
    env: Array<{
      key: string;
      value: string;
    }>;
    health_checks: Array<{
      http: {
        port: number;
        path: string;
      };
    }>;
  };
}

export class KoyebPreviewManager {
  private apiKey: string;
  private baseUrl = 'https://app.koyeb.com/v1';
  private dockerImage: string;

  constructor(apiKey: string, dockerImage: string) {
    this.apiKey = apiKey;
    this.dockerImage = dockerImage;
  }

  private async makeRequest<T>(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    body?: any,
  ): Promise<T> {
    console.log(
      `Making ${method} request to ${endpoint}`,
      body ? JSON.stringify(body, null, 2) : 'No body',
    );

    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method,
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: body ? JSON.stringify(body) : undefined,
      });

      const responseText = await response.text();

      if (!response.ok) {
        console.error(`Koyeb API error: ${response.status} - ${responseText}`);
        throw new Error(
          `Koyeb API error: ${response.status} - ${responseText}`,
        );
      }

      try {
        return JSON.parse(responseText);
      } catch (error) {
        console.error('Failed to parse response as JSON:', responseText);
        throw new Error(`Failed to parse Koyeb API response: ${error.message}`);
      }
    } catch (error) {
      console.error(`Request to ${endpoint} failed:`, error);
      throw error;
    }
  }

  async createApp(appName: string): Promise<KoyebApp> {
    return this.makeRequest<{ app: KoyebApp }>('/apps', 'POST', {
      name: appName,
    }).then((response) => response.app);
  }

  async createPreviewService(
    projectData: any,
  ): Promise<{ serviceId: string; appId: string; serviceUrl: string }> {
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substr(2, 9);
    const appName = `preview-app-${timestamp}-${randomId}`;
    const serviceName = `preview-service-${timestamp}-${randomId}`;

    // Step 1: Create App
    const app = (await this.createApp(appName)) as KoyebCreateAppResponse;

    // Step 2: Create Service with Docker deployment
    // Following the exact Koyeb API structure
    const servicePayload = {
      app_id: app.id,
      definition: {
        name: serviceName,
        type: 'WEB',
        routes: [
          {
            path: '/',
            port: 3000,
          },
          {
            path: '/inject-code',
            port: 8080,
          },
          {
            path: '/health',
            port: 8080,
          },
        ],
        ports: [
          {
            port: 3000,
            protocol: 'http',
          },
          {
            port: 8080,
            protocol: 'http',
          },
        ],
        env: [
          {
            key: 'NODE_ENV',
            value: 'development',
          },
        ],
        regions: ['sin'],
        instance_types: [
          {
            type: 'small',
          },
        ],
        scalings: [
          {
            min: 1,
            max: 1,
          },
        ],
        health_checks: [
          {
            http: {
              port: 8080,
              path: '/health',
            },
          },
        ],
        docker: {
          image: this.dockerImage,
        },
      },
    };

    try {
      const service = await this.makeRequest<{ service: KoyebService }>(
        '/services',
        'POST',
        servicePayload,
      ).then((response) => response.service);

      // Step 3: Wait for service to be ready
      const serviceUrl = await this.waitForServiceReady(service.id);

      // Schedule auto-destruction after 5 minutes
      setTimeout(
        () => {
          this.destroyService(service.id, app.id);
        },
        parseInt(process.env.PREVIEW_TIMEOUT || '300000'),
      );

      return {
        serviceId: service.id,
        appId: app.id,
        serviceUrl: `https://${app?.domains?.[0]?.name ?? ''}`,
      };
    } catch (error) {
      console.error('Service creation failed:', error);
      console.error(
        'Service payload:',
        JSON.stringify(servicePayload, null, 2),
      );

      // Clean up the app if service creation fails
      try {
        await this.makeRequest(`/apps/${app.id}`, 'DELETE');
        console.log(`App ${app.id} deleted after service creation failure`);
      } catch (cleanupError) {
        console.error(`Failed to clean up app ${app.id}:`, cleanupError);
      }

      throw error;
    }
  }

  async getService(serviceId: string): Promise<KoyebService> {
    return this.makeRequest<{ service: KoyebService }>(
      `/services/${serviceId}`,
    ).then((response) => response.service);
  }

  async getDeployment(deploymentId: string): Promise<KoyebDeployment> {
    return this.makeRequest<{ deployment: KoyebDeployment }>(
      `/deployments/${deploymentId}`,
    ).then((response) => response.deployment);
  }

  async waitForServiceReady(serviceId: string): Promise<string> {
    let attempts = 0;
    const maxAttempts = 30; // 5 minutes max wait

    while (attempts < maxAttempts) {
      try {
        const service = await this.getService(serviceId);

        if (service.latest_deployment_id) {
          const deployment = await this.getDeployment(
            service.latest_deployment_id,
          );

          if (deployment.status === 'HEALTHY') {
            // Get the public URL
            const publicUrl = await this.getServiceUrl(serviceId);
            return publicUrl;
          }

          if (
            deployment.status === 'ERROR' ||
            deployment.status === 'STOPPED'
          ) {
            throw new Error(
              `Service deployment failed: ${deployment.messages?.join(', ')}`,
            );
          }

          console.log(`Service ${serviceId} status: ${deployment.status}`);
        }

        await new Promise((resolve) => setTimeout(resolve, 10000)); // Wait 10s
        attempts++;
      } catch (error) {
        console.error(`Attempt ${attempts + 1} failed:`, error);
        attempts++;
        await new Promise((resolve) => setTimeout(resolve, 10000));
      }
    }

    throw new Error('Service failed to become ready within timeout');
  }

  async getServiceUrl(serviceId: string): Promise<string> {
    const service = await this.getService(serviceId);

    // Koyeb automatically assigns URLs based on service name and app ID
    const appId = service.app_id.split('-')[0];
    const serviceName = service.name;

    // Return the public URL for the service
    return `https://${serviceName}-${appId}.koyeb.app`;
  }

  async injectCode(
    serviceUrl: string,
    files: Record<string, string>,
  ): Promise<void> {
    // The startup.js script runs on port 8080 and handles both code injection and Next.js proxy
    const injectionUrl = `${serviceUrl}/inject-code`;

    const response = await fetch(injectionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Koyeb-Preview-System/1.0',
      },
      body: JSON.stringify({ files }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `Code injection failed: ${response.status} - ${errorText}`,
      );
    }

    const result = await response.json();
    console.log('Code injection successful:', result.message);
  }

  async destroyService(serviceId: string, appId: string): Promise<void> {
    try {
      await this.makeRequest(`/services/${serviceId}`, 'DELETE');
      console.log(`Service ${serviceId} deleted successfully`);

      await this.makeRequest(`/apps/${appId}`, 'DELETE');
      console.log(`App ${appId} deleted successfully`);
    } catch (error) {
      console.error(
        `Failed to destroy service ${serviceId} and app ${appId}:`,
        error,
      );
    }
  }
}
